import { useState } from 'react';

const Movies = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('all');

  // Extended movie data
  const movies = [
    {
      id: 1,
      title: "Your Name",
      year: 2016,
      genre: "Romance",
      rating: 8.2,
      duration: "106 min",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "Two teenagers share a profound, magical connection upon discovering they are swapping bodies."
    },
    {
      id: 2,
      title: "Spirited Away",
      year: 2001,
      genre: "Adventure",
      rating: 9.3,
      duration: "125 min",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "A young girl enters a world ruled by gods and witches where humans are changed into beasts."
    },
    {
      id: 3,
      title: "Demon Slayer: Mugen Train",
      year: 2020,
      genre: "Action",
      rating: 8.7,
      duration: "117 min",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "<PERSON>jiro and his friends board the Mugen Train to investigate demon disappearances."
    },
    {
      id: 4,
      title: "Princess Mononoke",
      year: 1997,
      genre: "Adventure",
      rating: 8.4,
      duration: "134 min",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "A young warrior becomes embroiled in a war between forest gods and humans."
    },
    {
      id: 5,
      title: "Akira",
      year: 1988,
      genre: "Action",
      rating: 8.0,
      duration: "124 min",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "A secret military project endangers Neo-Tokyo when it turns a biker gang member into a rampaging psychic psychopath."
    },
    {
      id: 6,
      title: "Weathering with You",
      year: 2019,
      genre: "Romance",
      rating: 7.5,
      duration: "112 min",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "A high school boy who has run away to Tokyo befriends a girl who appears to be able to manipulate the weather."
    }
  ];

  const genres = ['all', 'Action', 'Adventure', 'Romance', 'Drama', 'Comedy'];

  const filteredMovies = movies.filter(movie => {
    const matchesSearch = movie.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGenre = selectedGenre === 'all' || movie.genre === selectedGenre;
    return matchesSearch && matchesGenre;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-anime-primary text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl md:text-5xl font-bold text-center mb-4">
            Anime Movies
          </h1>
          <p className="text-xl text-center max-w-2xl mx-auto">
            Discover the best anime movies from classic masterpieces to modern hits
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search movies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-primary focus:border-transparent"
            />
          </div>
          <div>
            <select
              value={selectedGenre}
              onChange={(e) => setSelectedGenre(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-primary focus:border-transparent"
            >
              {genres.map(genre => (
                <option key={genre} value={genre}>
                  {genre === 'all' ? 'All Genres' : genre}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Movies Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredMovies.map((movie) => (
            <div key={movie.id} className="card group">
              <div className="relative overflow-hidden">
                <img
                  src={movie.image}
                  alt={movie.title}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4 bg-anime-primary text-white px-2 py-1 rounded-full text-sm font-semibold">
                  ★ {movie.rating}
                </div>
                <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
                  {movie.duration}
                </div>
              </div>
              <div className="p-4">
                <h3 className="text-lg font-bold text-anime-dark mb-2">{movie.title}</h3>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-anime-primary font-semibold">{movie.year}</span>
                  <span className="text-gray-500 text-sm">{movie.genre}</span>
                </div>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">{movie.description}</p>
                <button className="btn-primary w-full">
                  Watch Now
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredMovies.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No movies found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Movies;
