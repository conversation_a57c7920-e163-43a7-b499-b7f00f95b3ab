import { Link } from 'react-router-dom';

const MangaSection = () => {
  // Sample manga data
  const featuredManga = [
    {
      id: 1,
      title: "One Piece",
      author: "<PERSON><PERSON><PERSON><PERSON>",
      genre: "Adventure, Comedy",
      status: "Ongoing",
      chapters: "1000+",
      rating: 9.1,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "Follow Monkey D. <PERSON>ffy's epic journey to become the Pirate King and find the legendary treasure One Piece."
    },
    {
      id: 2,
      title: "Attack on Titan",
      author: "<PERSON><PERSON><PERSON>",
      genre: "Action, Drama",
      status: "Completed",
      chapters: "139",
      rating: 9.0,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "Humanity fights for survival against giant humanoid Titans in this dark and intense series."
    },
    {
      id: 3,
      title: "Demon Slayer",
      author: "<PERSON><PERSON><PERSON><PERSON>",
      genre: "Action, Supernatural",
      status: "Completed",
      chapters: "205",
      rating: 8.8,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "<PERSON>jiro's quest to save his demon-turned sister and avenge his family in Taisho-era Japan."
    },
    {
      id: 4,
      title: "My Hero Academia",
      author: "Kohei Horikoshi",
      genre: "Superhero, School",
      status: "Ongoing",
      chapters: "400+",
      rating: 8.5,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "In a world where superpowers are common, a quirkless boy dreams of becoming a hero."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-anime-dark mb-4">
            Popular Manga Series
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Dive into captivating manga stories that have defined generations and continue to inspire readers worldwide.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {featuredManga.map((manga) => (
            <div key={manga.id} className="card group">
              <div className="relative overflow-hidden">
                <img
                  src={manga.image}
                  alt={manga.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4 bg-anime-secondary text-white px-2 py-1 rounded-full text-sm font-semibold">
                  ★ {manga.rating}
                </div>
                <div className={`absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-semibold ${
                  manga.status === 'Ongoing' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'
                }`}>
                  {manga.status}
                </div>
              </div>
              <div className="p-4">
                <h3 className="text-lg font-bold text-anime-dark mb-1">{manga.title}</h3>
                <p className="text-anime-primary text-sm font-medium mb-2">{manga.author}</p>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-500 text-xs">{manga.genre}</span>
                  <span className="text-gray-500 text-xs">{manga.chapters} ch.</span>
                </div>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{manga.description}</p>
                <button className="btn-primary w-full text-sm">
                  Read Now
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            to="/manga"
            className="btn-secondary inline-flex items-center space-x-2"
          >
            <span>Explore All Manga</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MangaSection;
