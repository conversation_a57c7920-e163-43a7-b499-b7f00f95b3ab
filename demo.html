<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AnimeHub - Anime Movies & Manga</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        anime: {
                            primary: '#FF6B6B',
                            secondary: '#4ECDC4',
                            accent: '#45B7D1',
                            dark: '#2C3E50',
                            light: '#ECF0F1'
                        }
                    },
                    fontFamily: {
                        'anime': ['Poppins', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        body { font-family: 'Poppins', sans-serif; }
        .btn-primary { @apply bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200; }
        .btn-secondary { @apply bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200; }
        .card { @apply bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300; }
        .nav-link { @apply text-gray-700 hover:text-red-500 font-medium transition-colors duration-200; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">A</span>
                        </div>
                        <span class="text-xl font-bold text-gray-800">AnimeHub</span>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="nav-link text-red-500 border-b-2 border-red-500">Home</a>
                    <a href="#" class="nav-link">Anime Movies</a>
                    <a href="#" class="nav-link">Manga</a>
                    <button class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200">Search</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-red-500 to-blue-500 text-white py-20">
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Discover Amazing
                    <span class="block text-teal-300">Anime & Manga</span>
                </h1>
                <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                    Explore the world of anime movies and manga series. Find your next favorite story and join our community of anime enthusiasts.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-white text-red-500 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-colors duration-200">
                        Browse Anime Movies
                    </button>
                    <button class="border-2 border-white text-white hover:bg-white hover:text-red-500 font-semibold py-3 px-8 rounded-lg transition-colors duration-200">
                        Explore Manga
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Movies Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Featured Anime Movies</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Discover the most popular and critically acclaimed anime movies that have captured hearts worldwide.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Movie Card 1 -->
                <div class="card group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop" 
                             alt="Your Name" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-semibold">★ 8.2</div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Your Name</h3>
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-red-500 font-semibold">2016</span>
                            <span class="text-gray-500 text-sm">Romance, Drama</span>
                        </div>
                        <p class="text-gray-600 mb-4">Two teenagers share a profound, magical connection upon discovering they are swapping bodies.</p>
                        <button class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 w-full">Watch Now</button>
                    </div>
                </div>

                <!-- Movie Card 2 -->
                <div class="card group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop" 
                             alt="Spirited Away" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-semibold">★ 9.3</div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Spirited Away</h3>
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-red-500 font-semibold">2001</span>
                            <span class="text-gray-500 text-sm">Adventure, Family</span>
                        </div>
                        <p class="text-gray-600 mb-4">A young girl enters a world ruled by gods and witches where humans are changed into beasts.</p>
                        <button class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 w-full">Watch Now</button>
                    </div>
                </div>

                <!-- Movie Card 3 -->
                <div class="card group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop" 
                             alt="Demon Slayer" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-semibold">★ 8.7</div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Demon Slayer: Mugen Train</h3>
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-red-500 font-semibold">2020</span>
                            <span class="text-gray-500 text-sm">Action, Adventure</span>
                        </div>
                        <p class="text-gray-600 mb-4">Tanjiro and his friends board the Mugen Train to investigate demon disappearances.</p>
                        <button class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 w-full">Watch Now</button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 inline-flex items-center space-x-2">
                    <span>View All Movies</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Manga Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Popular Manga Series</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Dive into captivating manga stories that have defined generations and continue to inspire readers worldwide.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Manga Card 1 -->
                <div class="card group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop" 
                             alt="One Piece" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 bg-teal-500 text-white px-2 py-1 rounded-full text-sm font-semibold">★ 9.1</div>
                        <div class="absolute top-4 left-4 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Ongoing</div>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-1">One Piece</h3>
                        <p class="text-red-500 text-sm font-medium mb-2">Eiichiro Oda</p>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-500 text-xs">Adventure</span>
                            <span class="text-gray-500 text-xs">1000+ ch.</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Follow Monkey D. Luffy's epic journey to become the Pirate King.</p>
                        <button class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 w-full text-sm">Read Now</button>
                    </div>
                </div>

                <!-- Manga Card 2 -->
                <div class="card group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop" 
                             alt="Attack on Titan" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 bg-teal-500 text-white px-2 py-1 rounded-full text-sm font-semibold">★ 9.0</div>
                        <div class="absolute top-4 left-4 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Completed</div>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-1">Attack on Titan</h3>
                        <p class="text-red-500 text-sm font-medium mb-2">Hajime Isayama</p>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-500 text-xs">Action</span>
                            <span class="text-gray-500 text-xs">139 ch.</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Humanity fights for survival against giant humanoid Titans.</p>
                        <button class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 w-full text-sm">Read Now</button>
                    </div>
                </div>

                <!-- Manga Card 3 -->
                <div class="card group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop" 
                             alt="Demon Slayer" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 bg-teal-500 text-white px-2 py-1 rounded-full text-sm font-semibold">★ 8.8</div>
                        <div class="absolute top-4 left-4 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Completed</div>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-1">Demon Slayer</h3>
                        <p class="text-red-500 text-sm font-medium mb-2">Koyoharu Gotouge</p>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-500 text-xs">Action</span>
                            <span class="text-gray-500 text-xs">205 ch.</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Tanjiro's quest to save his demon-turned sister.</p>
                        <button class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 w-full text-sm">Read Now</button>
                    </div>
                </div>

                <!-- Manga Card 4 -->
                <div class="card group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop" 
                             alt="My Hero Academia" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 right-4 bg-teal-500 text-white px-2 py-1 rounded-full text-sm font-semibold">★ 8.5</div>
                        <div class="absolute top-4 left-4 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Ongoing</div>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-1">My Hero Academia</h3>
                        <p class="text-red-500 text-sm font-medium mb-2">Kohei Horikoshi</p>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-500 text-xs">Superhero</span>
                            <span class="text-gray-500 text-xs">400+ ch.</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">A quirkless boy dreams of becoming a hero.</p>
                        <button class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 w-full text-sm">Read Now</button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 inline-flex items-center space-x-2">
                    <span>Explore All Manga</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">A</span>
                        </div>
                        <span class="text-xl font-bold">AnimeHub</span>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        Your ultimate destination for anime movies and manga. Discover, explore, and immerse yourself in the wonderful world of Japanese animation and comics.
                    </p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Home</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Anime Movies</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Manga</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Top Rated</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Categories</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Action</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Romance</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Adventure</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-red-500 transition-colors">Comedy</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">© 2024 AnimeHub. All rights reserved. Made with ❤️ for anime fans worldwide.</p>
            </div>
        </div>
    </footer>
</body>
</html>
