<!DOCTYPE html>
<html lang="ku" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ئەنیمەڤێرس - جیهانت تەماشا بکە</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        verse: {
                            purple: '#9333EA',
                            pink: '#F472B6',
                            dark: '#1F2937',
                            light: '#F8FAFC'
                        }
                    },
                    fontFamily: {
                        'poppins': ['Noto Sans Arabic', 'Poppins', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'bounce-slow': 'bounce 3s infinite',
                        'pulse-slow': 'pulse 3s infinite'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Noto Sans Arabic', 'Poppins', sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px #F472B6; }
            to { box-shadow: 0 0 30px #9333EA, 0 0 40px #9333EA; }
        }

        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .anime-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .anime-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(147, 51, 234, 0.3);
        }

        .gradient-text {
            background: linear-gradient(135deg, #9333EA, #F472B6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .language-switcher {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 12px;
            border-radius: 12px;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button onclick="switchToEnglish()" class="px-4 py-2 text-sm bg-white/10 hover:bg-white/20 text-white rounded-lg ml-2 transition-all">🇺🇸 English</button>
        <button onclick="switchToKurdish()" class="px-4 py-2 text-sm bg-white/20 text-white rounded-lg hover:bg-white/30 transition-all">🟡🔴🟢 کوردی</button>
    </div>

    <!-- Navigation -->
    <nav class="glass-effect sticky top-0 z-50 border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <!-- AnimeVerse Logo -->
                        <div class="relative">
                            <div class="w-12 h-12 bg-gradient-to-br from-verse-purple to-verse-pink rounded-xl flex items-center justify-center animate-glow">
                                <div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                                    <span class="text-verse-purple font-black text-lg">ئڤ</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h1 class="text-2xl font-black text-white">ئەنیمەڤێرس</h1>
                            <p class="text-xs text-white/70 font-medium">جیهانت تەماشا بکە</p>
                        </div>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8 space-x-reverse">
                    <a href="#" class="text-white hover:text-verse-pink font-semibold transition-colors duration-200 border-b-2 border-verse-pink">ماڵەوە</a>
                    <a href="#" class="text-white/80 hover:text-verse-pink font-semibold transition-colors duration-200">فیلمی ئەنیمە</a>
                    <a href="#" class="text-white/80 hover:text-verse-pink font-semibold transition-colors duration-200">مانگا</a>
                    <button class="bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i data-lucide="search" class="w-4 h-4 inline ml-2"></i>
                        گەڕان
                    </button>
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center">
                    <button class="text-white hover:text-verse-pink transition-colors">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Animated Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-verse-purple via-purple-600 to-verse-pink"></div>
        <div class="absolute inset-0 bg-black/20"></div>

        <!-- Floating Elements -->
        <div class="absolute top-20 right-10 w-20 h-20 bg-white/10 rounded-full animate-float"></div>
        <div class="absolute top-40 left-20 w-16 h-16 bg-verse-pink/20 rounded-full animate-bounce-slow"></div>
        <div class="absolute bottom-20 right-20 w-24 h-24 bg-white/5 rounded-full animate-pulse-slow"></div>
        <div class="absolute bottom-40 left-10 w-12 h-12 bg-verse-purple/30 rounded-full animate-float" style="animation-delay: 2s;"></div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <!-- Anime Character Placeholder -->
            <div class="mb-8 flex justify-center">
                <div class="relative">
                    <div class="w-32 h-32 md:w-48 md:h-48 bg-gradient-to-br from-verse-pink to-verse-purple rounded-full flex items-center justify-center animate-float shadow-2xl">
                        <div class="w-24 h-24 md:w-36 md:h-36 bg-white rounded-full flex items-center justify-center">
                            <div class="text-4xl md:text-6xl">🎭</div>
                        </div>
                    </div>
                    <div class="absolute -top-2 -left-2 w-8 h-8 bg-yellow-400 rounded-full animate-bounce"></div>
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl lg:text-8xl font-black mb-6 text-white">
                <span class="block">بەخێربێیت بۆ</span>
                <span class="block gradient-text bg-gradient-to-r from-white to-verse-pink bg-clip-text text-transparent">ئەنیمەڤێرس</span>
            </h1>

            <p class="text-xl md:text-3xl font-bold text-white/90 mb-4">
                🌟 جیهانت تەماشا بکە 🌟
            </p>

            <p class="text-lg md:text-xl mb-12 max-w-4xl mx-auto text-white/80 leading-relaxed">
                خۆت نوقم بکە لە ئەزموونی کۆتایی ئەنیمە و مانگا! چیرۆکە سەرسوڕهێنەرەکان بدۆزەرەوە،
                کەسایەتییە لەبیرنەکراوەکان ببینە، و بەشداری ملیۆنان هەوادار بکە لە گەورەترین
                گەردوونی ئەنیمە کە تا ئێستا دروست کراوە.
            </p>

            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <button class="group bg-white text-verse-purple hover:bg-verse-purple hover:text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl min-w-[200px]">
                    <i data-lucide="play-circle" class="w-5 h-5 inline ml-2 group-hover:animate-spin"></i>
                    فیلمی ئەنیمە ببینە
                </button>
                <button class="group glass-effect text-white hover:bg-white/20 font-bold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl min-w-[200px]">
                    <i data-lucide="book-open" class="w-5 h-5 inline ml-2 group-hover:animate-bounce"></i>
                    مانگا بخوێنەرەوە
                </button>
            </div>

            <!-- Stats -->
            <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div class="glass-effect rounded-2xl p-6 text-center">
                    <div class="text-3xl font-black text-white mb-2">١٠٠٠+</div>
                    <div class="text-white/70 font-medium">فیلمی ئەنیمە</div>
                </div>
                <div class="glass-effect rounded-2xl p-6 text-center">
                    <div class="text-3xl font-black text-white mb-2">٥٠٠٠+</div>
                    <div class="text-white/70 font-medium">زنجیرە مانگا</div>
                </div>
                <div class="glass-effect rounded-2xl p-6 text-center">
                    <div class="text-3xl font-black text-white mb-2">١م+</div>
                    <div class="text-white/70 font-medium">هەوادارانی خۆش</div>
                </div>
                <div class="glass-effect rounded-2xl p-6 text-center">
                    <div class="text-3xl font-black text-white mb-2">٢٤/٧</div>
                    <div class="text-white/70 font-medium">پەخشی ڕاستەوخۆ</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Movies Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute top-10 right-10 w-32 h-32 bg-verse-purple rounded-full"></div>
            <div class="absolute bottom-10 left-10 w-24 h-24 bg-verse-pink rounded-full"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-black text-gray-800 mb-6">
                    🎬 فیلمی ئەنیمەی <span class="gradient-text">تایبەت</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    فیلمە ئەنیمە سەرسوڕهێنەر و پەسەندکراوەکان بدۆزەرەوە کە دڵ و مێشکی جیهان ڕاکێشاون.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Movie Card 1 -->
                <div class="anime-card rounded-3xl overflow-hidden group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop"
                             alt="ناوت" class="w-full h-72 object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div class="absolute top-4 left-4 bg-gradient-to-r from-verse-purple to-verse-pink text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg">
                            ⭐ ٨.٢
                        </div>
                        <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                            🔥 ترێند
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-black text-gray-800 mb-3">ناوت (Your Name)</h3>
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-verse-purple font-bold text-lg">٢٠١٦</span>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm font-medium">ڕۆمانسی • درامی</span>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">دوو گەنج پەیوەندییەکی سیحراوی قووڵیان هەیە کاتێک دەدۆزنەوە کە جەستەکانیان لە خەوندا ئاڵوگۆڕ دەکەن.</p>
                        <button class="w-full bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i data-lucide="play" class="w-4 h-4 inline ml-2"></i>
                            ئێستا ببینە
                        </button>
                    </div>
                </div>

                <!-- Movie Card 2 -->
                <div class="anime-card rounded-3xl overflow-hidden group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop"
                             alt="ڕۆحی دوورکەوتووە" class="w-full h-72 object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div class="absolute top-4 left-4 bg-gradient-to-r from-verse-purple to-verse-pink text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg">
                            ⭐ ٩.٣
                        </div>
                        <div class="absolute top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                            👑 شاکار
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-black text-gray-800 mb-3">ڕۆحی دوورکەوتووە</h3>
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-verse-purple font-bold text-lg">٢٠٠١</span>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm font-medium">سەرگەردانی • خێزانی</span>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">کچێکی گەنج دەچێتە ناو جیهانێکی سیحراوی کە لەلایەن خودا و جادووگەرانەوە فەرمانڕەوایی دەکرێت.</p>
                        <button class="w-full bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i data-lucide="play" class="w-4 h-4 inline ml-2"></i>
                            ئێستا ببینە
                        </button>
                    </div>
                </div>

                <!-- Movie Card 3 -->
                <div class="anime-card rounded-3xl overflow-hidden group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop"
                             alt="کوژەری ڕۆح" class="w-full h-72 object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div class="absolute top-4 left-4 bg-gradient-to-r from-verse-purple to-verse-pink text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg">
                            ⭐ ٨.٧
                        </div>
                        <div class="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                            ⚡ چالاکی
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-black text-gray-800 mb-3">کوژەری ڕۆح: شەمەندەفەری موگەن</h3>
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-verse-purple font-bold text-lg">٢٠٢٠</span>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm font-medium">چالاکی • سەرگەردانی</span>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">تانجیرۆ و هاوڕێکانی سواری شەمەندەفەری نهێنی موگەن دەبن بۆ لێکۆڵینەوە لە ونبوونی سەرنووسراوی.</p>
                        <button class="w-full bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i data-lucide="play" class="w-4 h-4 inline ml-2"></i>
                            ئێستا ببینە
                        </button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-16">
                <button class="bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl inline-flex items-center space-x-3 space-x-reverse">
                    <span class="text-lg">هەموو فیلمەکان ببینە</span>
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Manga Section -->
    <section class="py-20 bg-gradient-to-br from-verse-purple/5 to-verse-pink/5 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-20 left-20 w-40 h-40 bg-verse-purple rounded-full animate-pulse-slow"></div>
            <div class="absolute bottom-20 right-20 w-32 h-32 bg-verse-pink rounded-full animate-float"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-black text-gray-800 mb-6">
                    📚 زنجیرە مانگا <span class="gradient-text">بەناوبانگەکان</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    چیرۆکە سەرنجڕاکێشەکانی مانگا بخوێنەرەوە کە نەوەکانی پێناسە کردووە و بەردەوام ئیلهامی ملیۆنان خوێنەری جیهان دەبن.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Manga Card 1 -->
                <div class="anime-card rounded-2xl overflow-hidden group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop"
                             alt="یەک پارچە" class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div class="absolute top-3 left-3 bg-gradient-to-r from-verse-purple to-verse-pink text-white px-2 py-1 rounded-full text-xs font-bold">⭐ ٩.١</div>
                        <div class="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">📖 بەردەوامە</div>
                    </div>
                    <div class="p-5">
                        <h3 class="text-xl font-black text-gray-800 mb-2">یەک پارچە</h3>
                        <p class="text-verse-purple text-sm font-bold mb-3">ئەیچیرۆ ئۆدا</p>
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs font-medium">سەرگەردانی</span>
                            <span class="text-gray-500 text-xs font-medium">١٠٠٠+ بەش</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">گەشتی ئەفسانەوی مۆنکی دی لوفی بۆ بوون بە پاشای دزی دەریا بشوێنە.</p>
                        <button class="w-full bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-2 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 text-sm">
                            <i data-lucide="book-open" class="w-3 h-3 inline ml-1"></i>
                            ئێستا بخوێنەرەوە
                        </button>
                    </div>
                </div>

                <!-- Manga Card 2 -->
                <div class="anime-card rounded-2xl overflow-hidden group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop"
                             alt="هێرش بۆ سەر تایتان" class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div class="absolute top-3 left-3 bg-gradient-to-r from-verse-purple to-verse-pink text-white px-2 py-1 rounded-full text-xs font-bold">⭐ ٩.٠</div>
                        <div class="absolute top-3 right-3 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold">✅ تەواو بووە</div>
                    </div>
                    <div class="p-5">
                        <h3 class="text-xl font-black text-gray-800 mb-2">هێرش بۆ سەر تایتان</h3>
                        <p class="text-verse-purple text-sm font-bold mb-3">هاجیمە ئیسایاما</p>
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium">چالاکی</span>
                            <span class="text-gray-500 text-xs font-medium">١٣٩ بەش</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">مرۆڤایەتی بۆ مانەوە لە دژی تایتانە گەورەکان دەجەنگێت.</p>
                        <button class="w-full bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-2 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 text-sm">
                            <i data-lucide="book-open" class="w-3 h-3 inline ml-1"></i>
                            ئێستا بخوێنەرەوە
                        </button>
                    </div>
                </div>

                <!-- Manga Card 3 -->
                <div class="anime-card rounded-2xl overflow-hidden group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop"
                             alt="کوژەری ڕۆح" class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div class="absolute top-3 left-3 bg-gradient-to-r from-verse-purple to-verse-pink text-white px-2 py-1 rounded-full text-xs font-bold">⭐ ٨.٨</div>
                        <div class="absolute top-3 right-3 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold">✅ تەواو بووە</div>
                    </div>
                    <div class="p-5">
                        <h3 class="text-xl font-black text-gray-800 mb-2">کوژەری ڕۆح</h3>
                        <p class="text-verse-purple text-sm font-bold mb-3">کۆیۆهارو گۆتۆگە</p>
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium">چالاکی</span>
                            <span class="text-gray-500 text-xs font-medium">٢٠٥ بەش</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">گەشتی تانجیرۆ بۆ ڕزگارکردنی خوشکە ڕۆحبووەکەی.</p>
                        <button class="w-full bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-2 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 text-sm">
                            <i data-lucide="book-open" class="w-3 h-3 inline ml-1"></i>
                            ئێستا بخوێنەرەوە
                        </button>
                    </div>
                </div>

                <!-- Manga Card 4 -->
                <div class="anime-card rounded-2xl overflow-hidden group">
                    <div class="relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop"
                             alt="ئەکادیمیای پاڵەوانەکانم" class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <div class="absolute top-3 left-3 bg-gradient-to-r from-verse-purple to-verse-pink text-white px-2 py-1 rounded-full text-xs font-bold">⭐ ٨.٥</div>
                        <div class="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">📖 بەردەوامە</div>
                    </div>
                    <div class="p-5">
                        <h3 class="text-xl font-black text-gray-800 mb-2">ئەکادیمیای پاڵەوانەکانم</h3>
                        <p class="text-verse-purple text-sm font-bold mb-3">کۆهەی هۆرۆکۆشی</p>
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs font-medium">سوپەرهیرۆ</span>
                            <span class="text-gray-500 text-xs font-medium">٤٠٠+ بەش</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">کوڕێکی بێ توانا خەونی بوون بە پاڵەوان دەبینێت.</p>
                        <button class="w-full bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-2 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 text-sm">
                            <i data-lucide="book-open" class="w-3 h-3 inline ml-1"></i>
                            ئێستا بخوێنەرەوە
                        </button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-16">
                <button class="bg-gradient-to-r from-verse-purple to-verse-pink hover:from-verse-pink hover:to-verse-purple text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl inline-flex items-center space-x-3 space-x-reverse">
                    <span class="text-lg">هەموو مانگاکان بگەڕێ</span>
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 via-verse-dark to-black text-white py-16 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-10 right-10 w-32 h-32 bg-verse-purple rounded-full animate-pulse-slow"></div>
            <div class="absolute bottom-10 left-10 w-24 h-24 bg-verse-pink rounded-full animate-float"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 space-x-reverse mb-6">
                        <div class="w-12 h-12 bg-gradient-to-br from-verse-purple to-verse-pink rounded-xl flex items-center justify-center">
                            <div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                                <span class="text-verse-purple font-black text-lg">ئڤ</span>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-2xl font-black text-white">ئەنیمەڤێرس</h3>
                            <p class="text-xs text-white/70 font-medium">جیهانت تەماشا بکە</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md leading-relaxed">
                        شوێنی سەرەکیت بۆ فیلمی ئەنیمە و مانگا. بدۆزەرەوە، بگەڕێ، و خۆت نوقم بکە لە جیهانی سەرسوڕهێنەری ئەنیمەیشن و کۆمیکی ژاپۆنی. بەشداری ملیۆنان هەوادار بکە لە سەرانسەری جیهان!
                    </p>
                    <div class="flex space-x-4 space-x-reverse">
                        <button class="w-10 h-10 bg-verse-purple/20 hover:bg-verse-purple rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                            <i data-lucide="twitter" class="w-5 h-5"></i>
                        </button>
                        <button class="w-10 h-10 bg-verse-pink/20 hover:bg-verse-pink rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                            <i data-lucide="instagram" class="w-5 h-5"></i>
                        </button>
                        <button class="w-10 h-10 bg-blue-500/20 hover:bg-blue-500 rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                            <i data-lucide="facebook" class="w-5 h-5"></i>
                        </button>
                        <button class="w-10 h-10 bg-red-500/20 hover:bg-red-500 rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                            <i data-lucide="youtube" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-black mb-6 gradient-text">بەستەرە خێراکان</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="home" class="w-4 h-4 ml-2"></i>ماڵەوە</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="play-circle" class="w-4 h-4 ml-2"></i>فیلمی ئەنیمە</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="book-open" class="w-4 h-4 ml-2"></i>مانگا</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="star" class="w-4 h-4 ml-2"></i>بەرزترین پلەبەندی</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-black mb-6 gradient-text">پۆلەکان</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="zap" class="w-4 h-4 ml-2"></i>چالاکی</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="heart" class="w-4 h-4 ml-2"></i>ڕۆمانسی</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="compass" class="w-4 h-4 ml-2"></i>سەرگەردانی</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-verse-pink transition-colors duration-200 flex items-center"><i data-lucide="smile" class="w-4 h-4 ml-2"></i>کۆمیدی</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700/50 mt-12 pt-8 text-center">
                <p class="text-gray-300 text-lg">© ٢٠٢٤ <span class="gradient-text font-bold">ئەنیمەڤێرس</span>. هەموو مافەکان پارێزراون. بە خۆشەویستی دروست کراوە بۆ خۆشەویستانی ئەنیمە لە سەرانسەری جیهان.</p>
                <p class="text-gray-500 text-sm mt-2">🌟 جیهانت تەماشا بکە • چیرۆکەکەت بدۆزەرەوە 🌟</p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        function switchToEnglish() {
            window.location.href = 'demo.html';
        }

        function switchToKurdish() {
            // Already on Kurdish version
            alert('ئێستا لە وەشانی کوردیدایت!');
        }
    </script>
</body>
</html>
