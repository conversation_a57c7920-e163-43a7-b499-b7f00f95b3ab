import { useState } from 'react';

const Manga = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Extended manga data
  const manga = [
    {
      id: 1,
      title: "One Piece",
      author: "<PERSON><PERSON><PERSON><PERSON>da",
      genre: "Adventure",
      status: "Ongoing",
      chapters: "1000+",
      rating: 9.1,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "Follow Monkey D. <PERSON><PERSON>'s epic journey to become the Pirate King and find the legendary treasure One Piece."
    },
    {
      id: 2,
      title: "Attack on Titan",
      author: "<PERSON><PERSON><PERSON>",
      genre: "Action",
      status: "Completed",
      chapters: "139",
      rating: 9.0,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "Humanity fights for survival against giant humanoid Titans in this dark and intense series."
    },
    {
      id: 3,
      title: "Demon Slayer",
      author: "<PERSON><PERSON><PERSON><PERSON>ouge",
      genre: "Action",
      status: "Completed",
      chapters: "205",
      rating: 8.8,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "Tanjiro's quest to save his demon-turned sister and avenge his family in Taisho-era Japan."
    },
    {
      id: 4,
      title: "My Hero Academia",
      author: "Kohei Horikoshi",
      genre: "Superhero",
      status: "Ongoing",
      chapters: "400+",
      rating: 8.5,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "In a world where superpowers are common, a quirkless boy dreams of becoming a hero."
    },
    {
      id: 5,
      title: "Naruto",
      author: "Masashi Kishimoto",
      genre: "Action",
      status: "Completed",
      chapters: "700",
      rating: 8.7,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "A young ninja's journey to become the strongest ninja and leader of his village."
    },
    {
      id: 6,
      title: "Death Note",
      author: "Tsugumi Ohba",
      genre: "Thriller",
      status: "Completed",
      chapters: "108",
      rating: 9.0,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "A high school student discovers a supernatural notebook that kills anyone whose name is written in it."
    },
    {
      id: 7,
      title: "Jujutsu Kaisen",
      author: "Gege Akutami",
      genre: "Action",
      status: "Ongoing",
      chapters: "250+",
      rating: 8.6,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "Students at a school for jujutsu sorcerers learn to combat supernatural curses."
    },
    {
      id: 8,
      title: "Chainsaw Man",
      author: "Tatsuki Fujimoto",
      genre: "Action",
      status: "Ongoing",
      chapters: "150+",
      rating: 8.4,
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=600&fit=crop",
      description: "A young man with chainsaw powers works as a devil hunter to pay off his debts."
    }
  ];

  const genres = ['all', 'Action', 'Adventure', 'Romance', 'Thriller', 'Superhero', 'Comedy'];
  const statuses = ['all', 'Ongoing', 'Completed'];

  const filteredManga = manga.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.author.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGenre = selectedGenre === 'all' || item.genre === selectedGenre;
    const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus;
    return matchesSearch && matchesGenre && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-anime-secondary text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl md:text-5xl font-bold text-center mb-4">
            Manga Collection
          </h1>
          <p className="text-xl text-center max-w-2xl mx-auto">
            Explore thousands of manga series from various genres and discover your next favorite read
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search manga or author..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-secondary focus:border-transparent"
            />
          </div>
          <div>
            <select
              value={selectedGenre}
              onChange={(e) => setSelectedGenre(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-secondary focus:border-transparent"
            >
              {genres.map(genre => (
                <option key={genre} value={genre}>
                  {genre === 'all' ? 'All Genres' : genre}
                </option>
              ))}
            </select>
          </div>
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-secondary focus:border-transparent"
            >
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status === 'all' ? 'All Status' : status}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Manga Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredManga.map((item) => (
            <div key={item.id} className="card group">
              <div className="relative overflow-hidden">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4 bg-anime-secondary text-white px-2 py-1 rounded-full text-sm font-semibold">
                  ★ {item.rating}
                </div>
                <div className={`absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-semibold ${
                  item.status === 'Ongoing' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'
                }`}>
                  {item.status}
                </div>
              </div>
              <div className="p-4">
                <h3 className="text-lg font-bold text-anime-dark mb-1">{item.title}</h3>
                <p className="text-anime-secondary text-sm font-medium mb-2">{item.author}</p>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-500 text-sm">{item.genre}</span>
                  <span className="text-gray-500 text-sm">{item.chapters} ch.</span>
                </div>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">{item.description}</p>
                <button className="btn-secondary w-full">
                  Read Now
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredManga.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No manga found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Manga;
