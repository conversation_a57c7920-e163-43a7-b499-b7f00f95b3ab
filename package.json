{"name": "first_anime_move_website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "serve:html": "live-server --port=3000 --open=demo.html", "serve:kurdish": "live-server --port=3000 --open=demo-kurdish.html", "start:demo": "live-server --port=8080 --open=demo.html", "start:kurdish": "live-server --port=8080 --open=demo-kurdish.html"}, "dependencies": {"i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-router-dom": "^7.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}}