import { Link } from 'react-router-dom';

const MovieSection = () => {
  // Sample anime movie data
  const featuredMovies = [
    {
      id: 1,
      title: "Your Name",
      year: 2016,
      genre: "Romance, Drama",
      rating: 8.2,
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "Two teenagers share a profound, magical connection upon discovering they are swapping bodies."
    },
    {
      id: 2,
      title: "Spirited Away",
      year: 2001,
      genre: "Adventure, Family",
      rating: 9.3,
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "A young girl enters a world ruled by gods and witches where humans are changed into beasts."
    },
    {
      id: 3,
      title: "Demon Slayer: Mugen Train",
      year: 2020,
      genre: "Action, Adventure",
      rating: 8.7,
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
      description: "<PERSON><PERSON><PERSON> and his friends board the Mugen Train to investigate demon disappearances."
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-anime-dark mb-4">
            Featured Anime Movies
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover the most popular and critically acclaimed anime movies that have captured hearts worldwide.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredMovies.map((movie) => (
            <div key={movie.id} className="card group">
              <div className="relative overflow-hidden">
                <img
                  src={movie.image}
                  alt={movie.title}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4 bg-anime-primary text-white px-2 py-1 rounded-full text-sm font-semibold">
                  ★ {movie.rating}
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-anime-dark mb-2">{movie.title}</h3>
                <div className="flex items-center justify-between mb-3">
                  <span className="text-anime-primary font-semibold">{movie.year}</span>
                  <span className="text-gray-500 text-sm">{movie.genre}</span>
                </div>
                <p className="text-gray-600 mb-4 line-clamp-3">{movie.description}</p>
                <button className="btn-primary w-full">
                  Watch Now
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            to="/movies"
            className="btn-secondary inline-flex items-center space-x-2"
          >
            <span>View All Movies</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MovieSection;
