import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const { t } = useTranslation();

  const isActive = (path) => location.pathname === path;

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-anime-primary rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">A</span>
              </div>
              <span className="text-xl font-bold text-anime-dark">{t('nav.brand')}</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`nav-link ${
                isActive('/') ? 'text-anime-primary border-b-2 border-anime-primary' : ''
              }`}
            >
              {t('nav.home')}
            </Link>
            <Link
              to="/movies"
              className={`nav-link ${
                isActive('/movies') ? 'text-anime-primary border-b-2 border-anime-primary' : ''
              }`}
            >
              {t('nav.movies')}
            </Link>
            <Link
              to="/manga"
              className={`nav-link ${
                isActive('/manga') ? 'text-anime-primary border-b-2 border-anime-primary' : ''
              }`}
            >
              {t('nav.manga')}
            </Link>
            <LanguageSwitcher />
            <button className="btn-primary">
              {t('nav.search')}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-700 hover:text-anime-primary focus:outline-none focus:text-anime-primary"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <Link
                to="/"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive('/') ? 'text-anime-primary bg-red-50' : 'text-gray-700 hover:text-anime-primary'
                }`}
                onClick={() => setIsOpen(false)}
              >
                {t('nav.home')}
              </Link>
              <Link
                to="/movies"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive('/movies') ? 'text-anime-primary bg-red-50' : 'text-gray-700 hover:text-anime-primary'
                }`}
                onClick={() => setIsOpen(false)}
              >
                {t('nav.movies')}
              </Link>
              <Link
                to="/manga"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive('/manga') ? 'text-anime-primary bg-red-50' : 'text-gray-700 hover:text-anime-primary'
                }`}
                onClick={() => setIsOpen(false)}
              >
                {t('nav.manga')}
              </Link>
              <div className="px-3 py-2">
                <LanguageSwitcher />
              </div>
              <button className="btn-primary w-full mt-2">
                {t('nav.search')}
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
